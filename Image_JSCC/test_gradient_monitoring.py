#!/usr/bin/env python3
"""
测试梯度监控功能
"""

import tensorflow as tf
import numpy as np
import sys
import os

# 设置GPU内存增长
gpus = tf.config.experimental.list_physical_devices('GPU')
if gpus:
    try:
        tf.config.experimental.set_visible_devices(gpus[0], 'GPU')
        tf.config.experimental.set_memory_growth(gpus[0], True)
    except RuntimeError as e:
        print(e)

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import ImageJSCCConfig
from model.image_jscc_model import create_model
from train_image_jscc import train_step

def test_gradient_monitoring():
    """测试梯度监控功能"""
    print("=" * 60)
    print("测试梯度监控功能")
    print("=" * 60)
    
    # 创建配置
    config = ImageJSCCConfig()
    print(f"梯度裁剪阈值: {config.gradient_clip_norm}")
    
    # 创建模型
    print("\n创建模型...")
    model = create_model(config)
    
    # 创建优化器和损失函数
    optimizer = tf.keras.optimizers.Adam(learning_rate=config.initial_lr)
    loss_fn = tf.keras.losses.MeanSquaredError()
    
    # 创建测试输入
    batch_size = 4
    test_input = tf.random.uniform([
        batch_size, 
        config.image_height, 
        config.image_width, 
        config.image_channels
    ])
    
    print(f"\n输入形状: {test_input.shape}")
    
    # 测试多次训练步骤，观察梯度变化
    print("\n测试梯度监控...")
    print("Step | Loss     | Grad Norm | Clipped Norm | Clipped?")
    print("-" * 55)
    
    for step in range(10):
        # 执行训练步骤
        loss, grad_norm, clipped_grad_norm = train_step(
            model, optimizer, loss_fn, test_input, config.gradient_clip_norm
        )
        
        # 检查是否发生了梯度裁剪
        was_clipped = grad_norm.numpy() > config.gradient_clip_norm
        clipped_indicator = "YES" if was_clipped else "NO"
        
        print(f"{step:4d} | {loss.numpy():.6f} | {grad_norm.numpy():.6f} | {clipped_grad_norm.numpy():.6f} | {clipped_indicator:>8s}")
    
    print("\n✅ 梯度监控测试完成！")

def test_gradient_explosion():
    """测试梯度爆炸情况"""
    print("\n" + "=" * 60)
    print("测试梯度爆炸情况（使用大学习率）")
    print("=" * 60)
    
    config = ImageJSCCConfig()
    model = create_model(config)
    
    # 使用很大的学习率来模拟梯度爆炸
    large_lr_optimizer = tf.keras.optimizers.Adam(learning_rate=0.1)  # 很大的学习率
    loss_fn = tf.keras.losses.MeanSquaredError()
    
    test_input = tf.random.uniform([2, config.image_height, config.image_width, config.image_channels])
    
    print("使用大学习率 (0.1) 测试梯度爆炸...")
    print("Step | Loss     | Grad Norm | Clipped Norm | Clipped?")
    print("-" * 55)
    
    for step in range(5):
        try:
            loss, grad_norm, clipped_grad_norm = train_step(
                model, large_lr_optimizer, loss_fn, test_input, config.gradient_clip_norm
            )
            
            was_clipped = grad_norm.numpy() > config.gradient_clip_norm
            clipped_indicator = "YES" if was_clipped else "NO"
            
            print(f"{step:4d} | {loss.numpy():.6f} | {grad_norm.numpy():.6f} | {clipped_grad_norm.numpy():.6f} | {clipped_indicator:>8s}")
            
            # 如果梯度范数过大，警告
            if grad_norm.numpy() > 10.0:
                print(f"     ⚠️  警告：梯度范数过大 ({grad_norm.numpy():.2f})，可能发生梯度爆炸！")
                
        except Exception as e:
            print(f"     ❌ 第{step}步发生错误: {e}")
            break

def test_gradient_vanishing():
    """测试梯度消失情况"""
    print("\n" + "=" * 60)
    print("测试梯度消失情况（使用很小学习率）")
    print("=" * 60)
    
    config = ImageJSCCConfig()
    model = create_model(config)
    
    # 使用很小的学习率
    small_lr_optimizer = tf.keras.optimizers.Adam(learning_rate=1e-8)
    loss_fn = tf.keras.losses.MeanSquaredError()
    
    test_input = tf.random.uniform([2, config.image_height, config.image_width, config.image_channels])
    
    print("使用小学习率 (1e-8) 测试梯度消失...")
    print("Step | Loss     | Grad Norm | Clipped Norm")
    print("-" * 45)
    
    for step in range(5):
        loss, grad_norm, clipped_grad_norm = train_step(
            model, small_lr_optimizer, loss_fn, test_input, config.gradient_clip_norm
        )
        
        print(f"{step:4d} | {loss.numpy():.6f} | {grad_norm.numpy():.6f} | {clipped_grad_norm.numpy():.6f}")
        
        # 如果梯度范数过小，警告
        if grad_norm.numpy() < 1e-6:
            print(f"     ⚠️  警告：梯度范数过小 ({grad_norm.numpy():.2e})，可能发生梯度消失！")

if __name__ == "__main__":
    try:
        test_gradient_monitoring()
        test_gradient_explosion()
        test_gradient_vanishing()
        
        print("\n🎉 所有梯度监控测试完成！")
        print("\n📊 现在你可以在训练时实时监控梯度状态：")
        print("   - grad_norm: 原始梯度范数")
        print("   - clipped_grad_norm: 裁剪后梯度范数")
        print("   - gradient_clipping_ratio: 梯度裁剪比例")
        print("\n💡 建议的梯度范数范围：")
        print("   - 正常范围: 0.1 - 2.0")
        print("   - 梯度爆炸: > 5.0")
        print("   - 梯度消失: < 0.01")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
