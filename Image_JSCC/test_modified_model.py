#!/usr/bin/env python3
"""
测试修改后的Image_JSCC模型
验证删除全局平均池化后的模型是否正常工作
"""

import tensorflow as tf
import numpy as np
import sys
import os

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import ImageJSCCConfig
from model.image_jscc_model import create_model

def test_model_shapes():
    """测试模型的输入输出形状"""
    print("=" * 60)
    print("测试修改后的Image_JSCC模型")
    print("=" * 60)
    
    # 创建配置
    config = ImageJSCCConfig()
    print(f"图像尺寸: {config.image_height}x{config.image_width}x{config.image_channels}")
    print(f"压缩比: {config.compression_ratio}")
    print(f"嵌入维度: {config.embedding_dim}")
    
    # 创建模型
    print("\n创建模型...")
    model = create_model(config)
    
    # 创建测试输入
    batch_size = 2
    test_input = tf.random.uniform([
        batch_size, 
        config.image_height, 
        config.image_width, 
        config.image_channels
    ])
    
    print(f"\n输入形状: {test_input.shape}")
    
    # 测试编码器
    print("\n测试编码器...")
    encoded_features = model.encode(test_input, training=False)
    print(f"编码器输出形状: {encoded_features.shape}")
    
    # 计算特征维度
    compressed_height = config.image_height // 8  # 32
    compressed_width = config.image_width // 8    # 64
    spatial_features = compressed_height * compressed_width * config.embedding_dim
    compressed_features = (compressed_height * compressed_width) // config.compression_ratio
    
    print(f"空间特征维度: {spatial_features}")
    print(f"压缩特征维度: {compressed_features}")
    
    # 测试完整的前向传播
    print("\n测试完整前向传播...")
    output = model(test_input, snr_db=config.snr_dB, training=False)
    print(f"重建图像形状: {output.shape}")
    print(f"重建图像值范围: [{tf.reduce_min(output):.3f}, {tf.reduce_max(output):.3f}]")
    
    # 验证形状是否正确
    assert output.shape == test_input.shape, f"输出形状 {output.shape} 与输入形状 {test_input.shape} 不匹配"
    
    # 计算压缩比
    input_size = np.prod(test_input.shape[1:])  # 不包括batch维度
    compressed_size = encoded_features.shape[1]
    actual_compression_ratio = input_size / compressed_size
    
    print(f"\n压缩比分析:")
    print(f"输入大小: {input_size}")
    print(f"压缩后大小: {compressed_size}")
    print(f"实际压缩比: {actual_compression_ratio:.2f}")
    
    # 显示模型摘要
    print("\n模型摘要:")
    model.summary_custom()
    
    print("\n✅ 所有测试通过！")

def test_different_snr():
    """测试不同信噪比下的模型性能"""
    print("\n" + "=" * 60)
    print("测试不同信噪比")
    print("=" * 60)
    
    config = ImageJSCCConfig()
    model = create_model(config)
    
    # 创建测试输入
    test_input = tf.random.uniform([1, config.image_height, config.image_width, config.image_channels])
    
    snr_values = [0, 5, 10, 15, 20]
    
    for snr in snr_values:
        output = model(test_input, snr_db=snr, training=False)
        mse = tf.reduce_mean(tf.square(test_input - output))
        psnr = 20 * tf.math.log(1.0 / tf.sqrt(mse)) / tf.math.log(10.0)
        
        print(f"SNR: {snr:2d} dB, MSE: {mse:.6f}, PSNR: {psnr:.2f} dB")

if __name__ == "__main__":
    # 设置GPU内存增长
    gpus = tf.config.experimental.list_physical_devices('GPU')
    if gpus:
        try:
            for gpu in gpus:
                tf.config.experimental.set_memory_growth(gpu, True)
        except RuntimeError as e:
            print(e)
    
    try:
        test_model_shapes()
        test_different_snr()
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
