nohup: 忽略输入
WARNING: All log messages before absl::InitializeLog() is called are written to STDERR
E0000 00:00:1754376959.689753 1244821 cuda_dnn.cc:8579] Unable to register cuDNN factory: Attempting to register factory for plugin cuDNN when one has already been registered
E0000 00:00:1754376959.693296 1244821 cuda_blas.cc:1407] Unable to register cuBLAS factory: Attempting to register factory for plugin cuBLAS when one has already been registered
W0000 00:00:1754376959.704915 1244821 computation_placer.cc:177] computation placer already registered. Please check linkage and avoid linking the same target more than once.
W0000 00:00:1754376959.704955 1244821 computation_placer.cc:177] computation placer already registered. Please check linkage and avoid linking the same target more than once.
W0000 00:00:1754376959.704958 1244821 computation_placer.cc:177] computation placer already registered. Please check linkage and avoid linking the same target more than once.
W0000 00:00:1754376959.704961 1244821 computation_placer.cc:177] computation placer already registered. Please check linkage and avoid linking the same target more than once.
wandb: Tracking run with wandb version 0.21.0
wandb: W&B syncing is set to `offline` in this directory. Run `wandb online` or set WANDB_MODE=online to enable cloud syncing.
I0000 00:00:1754376962.671237 1244821 gpu_device.cc:2019] Created device /job:localhost/replica:0/task:0/device:GPU:0 with 37858 MB memory:  -> device: 0, name: NVIDIA RTX 6000 Ada Generation, pci bus id: 0000:4e:00.0, compute capability: 8.9
/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/layers/layer.py:421: UserWarning: `build()` was called on layer 'image_encoder', however the layer does not have a `build()` method implemented and it looks like it has unbuilt state. This will cause the layer to be marked as built, despite not being actually built, which may cause failures down the line. Make sure to implement a proper `build()` method.
  warnings.warn(
/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/layers/layer.py:421: UserWarning: `build()` was called on layer 'image_decoder', however the layer does not have a `build()` method implemented and it looks like it has unbuilt state. This will cause the layer to be marked as built, despite not being actually built, which may cause failures down the line. Make sure to implement a proper `build()` method.
  warnings.warn(
/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/layers/layer.py:421: UserWarning: `build()` was called on layer 'image_jscc_model', however the layer does not have a `build()` method implemented and it looks like it has unbuilt state. This will cause the layer to be marked as built, despite not being actually built, which may cause failures down the line. Make sure to implement a proper `build()` method.
  warnings.warn(
I0000 00:00:1754376963.695996 1244821 cuda_dnn.cc:529] Loaded cuDNN version 90501
Configuration loaded:
  Image size: 256x512x3
  Batch size: 4
  Epochs: 300
  Compression ratio: 1
  SNR: 10 dB
  Channel type: AWGN

Loading datasets...
Found 2975 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/train
Found 500 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/val
Steps per epoch: 743

Creating model...
============================================================
Image JSCC Model Summary
============================================================
Input shape: (256, 512, 3)
Compression ratio: 1
Quantization bits: 8
Channel type: AWGN
SNR: 10 dB
Compressed features: 2048
Transmitted bits: 16384
Actual compression ratio: 192.00
============================================================
Encoder parameters: 1,406,176
Decoder parameters: 0
Total trainable parameters: 1,406,176
============================================================

Starting training...
Logs will be saved to: /home/<USER>/GESCO/Image_JSCC/logs/ImageJSCC_snr_10dB_comp_1_20250805_145602/
Checkpoints will be saved to: /home/<USER>/GESCO/Image_JSCC/checkpoints/ImageJSCC_snr_10dB_comp_1_20250805_145602/

Epoch 1/300
Found 2975 images in /home/<USER>/GESCO/data/Cityscapes/leftImg8bit/train

Training:   0%|          | 0/743 [00:00<?, ?it/s]
Training:   0%|          | 0/743 [00:03<?, ?it/s]
Traceback (most recent call last):
  File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 328, in <module>
    main()
  File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 223, in main
    loss = train_step(model, optimizer, loss_fn, x_batch, config.gradient_clip_norm)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/tensorflow/python/util/traceback_utils.py", line 153, in error_handler
    raise e.with_traceback(filtered_tb) from None
  File "/tmp/__autograph_generated_file4xupbzpi.py", line 12, in tf__train_step
    y_pred = ag__.converted_call(ag__.ld(model), (ag__.ld(x_batch),), dict(training=True), fscope)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/utils/traceback_utils.py", line 122, in error_handler
    raise e.with_traceback(filtered_tb) from None
  File "/home/<USER>/GESCO/Image_JSCC/model/image_jscc_model.py", line 88, in call
    reconstructed = self.decoder(received_features, training=training)
  File "/home/<USER>/GESCO/Image_JSCC/layers/image_decoder.py", line 143, in call
    x = tf.keras.layers.Dense(2048, activation='relu')(inputs)
ValueError: in user code:

    File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 47, in train_step  *
        y_pred = model(x_batch, training=True)
    File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/utils/traceback_utils.py", line 122, in error_handler  **
        raise e.with_traceback(filtered_tb) from None
    File "/home/<USER>/GESCO/Image_JSCC/model/image_jscc_model.py", line 88, in call
        reconstructed = self.decoder(received_features, training=training)
    File "/home/<USER>/GESCO/Image_JSCC/layers/image_decoder.py", line 143, in call
        x = tf.keras.layers.Dense(2048, activation='relu')(inputs)

    ValueError: Exception encountered when calling ImageDecoder.call().
    
    [1mtf.function only supports singleton tf.Variables created on the first call. Make sure the tf.Variable is only created once or created outside tf.function. See https://www.tensorflow.org/guide/function#creating_tfvariables for more information.[0m
    
    Arguments received by ImageDecoder.call():
      • inputs=tf.Tensor(shape=(4, 2048), dtype=float32)
      • training=True

Traceback (most recent call last):
  File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 328, in <module>
    main()
  File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 223, in main
    loss = train_step(model, optimizer, loss_fn, x_batch, config.gradient_clip_norm)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/tensorflow/python/util/traceback_utils.py", line 153, in error_handler
    raise e.with_traceback(filtered_tb) from None
  File "/tmp/__autograph_generated_file4xupbzpi.py", line 12, in tf__train_step
    y_pred = ag__.converted_call(ag__.ld(model), (ag__.ld(x_batch),), dict(training=True), fscope)
  File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/utils/traceback_utils.py", line 122, in error_handler
    raise e.with_traceback(filtered_tb) from None
  File "/home/<USER>/GESCO/Image_JSCC/model/image_jscc_model.py", line 88, in call
    reconstructed = self.decoder(received_features, training=training)
  File "/home/<USER>/GESCO/Image_JSCC/layers/image_decoder.py", line 143, in call
    x = tf.keras.layers.Dense(2048, activation='relu')(inputs)
ValueError: in user code:

    File "/home/<USER>/GESCO/Image_JSCC/train_image_jscc.py", line 47, in train_step  *
        y_pred = model(x_batch, training=True)
    File "/home/<USER>/anaconda3/envs/GAI/lib/python3.10/site-packages/keras/src/utils/traceback_utils.py", line 122, in error_handler  **
        raise e.with_traceback(filtered_tb) from None
    File "/home/<USER>/GESCO/Image_JSCC/model/image_jscc_model.py", line 88, in call
        reconstructed = self.decoder(received_features, training=training)
    File "/home/<USER>/GESCO/Image_JSCC/layers/image_decoder.py", line 143, in call
        x = tf.keras.layers.Dense(2048, activation='relu')(inputs)

    ValueError: Exception encountered when calling ImageDecoder.call().
    
    [1mtf.function only supports singleton tf.Variables created on the first call. Make sure the tf.Variable is only created once or created outside tf.function. See https://www.tensorflow.org/guide/function#creating_tfvariables for more information.[0m
    
    Arguments received by ImageDecoder.call():
      • inputs=tf.Tensor(shape=(4, 2048), dtype=float32)
      • training=True

[1;34mwandb[0m: 
[1;34mwandb[0m: You can sync this run to the cloud by running:
[1;34mwandb[0m: [1mwandb sync /home/<USER>/GESCO/Image_JSCC/wandb/offline-run-20250805_145602-hh7ulikd[0m
[1;34mwandb[0m: Find logs at: [1;35mwandb/offline-run-20250805_145602-hh7ulikd/logs[0m
