import tensorflow as tf
from tensorflow.keras import layers
import numpy as np
from .attention_modules import MultiHeadAttention

class ImageDecoder(tf.keras.layers.Layer):
    def __init__(self, config, **kwargs):
        super(ImageDecoder, self).__init__(**kwargs)
        
        self.config = config
        self.embedding_dim = config.embedding_dim
        self.num_heads = config.TF_heads
        self.num_layers = config.dec_TF_layers
        self.compression_ratio = config.compression_ratio
        
        # 图像参数
        self.image_height = config.image_height
        self.image_width = config.image_width
        self.image_channels = config.image_channels
        
        # 压缩特征参数
        self.compressed_height = config.image_height // 8  # 32
        self.compressed_width = config.image_width // 8    # 64
        self.feature_dim = self.compressed_height * self.compressed_width  # 2048
        self.compressed_features = self.feature_dim // self.compression_ratio
        
        # 特征重建层
        self.feature_reconstruction = self._build_feature_reconstruction()
        
        # Transformer解码器层
        self.transformer_layers = self._build_transformer_layers()
        
        # CNN重建层
        self.conv_reconstruction = self._build_conv_reconstruction()
        
    def _build_feature_reconstruction(self):
        """构建特征重建层"""
        reconstruction_layers = tf.keras.Sequential([
            layers.Dense(self.embedding_dim // 2, activation='relu'),
            layers.Dropout(0.1),
            layers.Dense(self.embedding_dim, activation='relu'),
            layers.Dropout(0.1),
            layers.Dense(self.feature_dim * self.embedding_dim, activation='relu'),
            layers.Reshape([self.feature_dim, self.embedding_dim])
        ])
        return reconstruction_layers
    
    def _build_transformer_layers(self):
        """构建Transformer解码器层"""
        transformer_layers = []
        for i in range(self.num_layers):
            # 多头注意力层
            attention_layer = MultiHeadAttention(
                head_num=self.num_heads,
                name=f'decoder_attention_{i}'
            )
            
            # 前馈网络
            ffn_layer = tf.keras.Sequential([
                layers.Dense(self.embedding_dim * 4, activation='relu'),
                layers.Dropout(0.1),
                layers.Dense(self.embedding_dim),
                layers.Dropout(0.1)
            ], name=f'decoder_ffn_{i}')
            
            # 层归一化
            norm1 = layers.LayerNormalization(name=f'decoder_norm1_{i}')
            norm2 = layers.LayerNormalization(name=f'decoder_norm2_{i}')
            
            transformer_layers.append({
                'attention': attention_layer,
                'ffn': ffn_layer,
                'norm1': norm1,
                'norm2': norm2
            })
        
        return transformer_layers
    
    def _build_conv_reconstruction(self):
        """构建CNN重建层 - 简化版本"""
        conv_layers = tf.keras.Sequential([
            # 直接从压缩特征重建到图像
            # 第一步：扩展到合适的特征维度
            layers.Dense(self.compressed_height * self.compressed_width * 128, activation='relu'),
            layers.Reshape([self.compressed_height, self.compressed_width, 128]),

            # 第一个上采样块 (32x64 -> 64x128)
            layers.Conv2DTranspose(64, 4, strides=2, padding='same', activation='relu'),
            layers.BatchNormalization(),

            # 第二个上采样块 (64x128 -> 128x256)
            layers.Conv2DTranspose(32, 4, strides=2, padding='same', activation='relu'),
            layers.BatchNormalization(),

            # 第三个上采样块 (128x256 -> 256x512)
            layers.Conv2DTranspose(16, 4, strides=2, padding='same', activation='relu'),
            layers.BatchNormalization(),

            # 最终输出层 - 直接输出到[0,1]
            layers.Conv2D(self.image_channels, 3, strides=1, padding='same', activation='sigmoid'),
        ])
        return conv_layers
    
    def _positional_encoding(self, seq_len, d_model):
        """生成正弦位置编码"""
        position = np.arange(seq_len)[:, np.newaxis]
        div_term = np.exp(np.arange(0, d_model, 2) * -(np.log(10000.0) / d_model))
        
        pos_encoding = np.zeros((seq_len, d_model))
        pos_encoding[:, 0::2] = np.sin(position * div_term)
        pos_encoding[:, 1::2] = np.cos(position * div_term)
        
        return tf.constant(pos_encoding, dtype=tf.float32)

    def _cnn_upsampling(self, x, training=False):
        """CNN上采样重建"""
        # x shape: [batch, compressed_height, compressed_width, embedding_dim]

        # 第一个上采样块 (32x64 -> 64x128)
        x = layers.Conv2DTranspose(128, 4, strides=2, padding='same', activation='relu')(x)
        x = layers.BatchNormalization()(x, training=training)

        # 第二个上采样块 (64x128 -> 128x256)
        x = layers.Conv2DTranspose(64, 4, strides=2, padding='same', activation='relu')(x)
        x = layers.BatchNormalization()(x, training=training)

        # 第三个上采样块 (128x256 -> 256x512)
        x = layers.Conv2DTranspose(32, 4, strides=2, padding='same', activation='relu')(x)
        x = layers.BatchNormalization()(x, training=training)

        # 最终输出层
        x = layers.Conv2D(self.image_channels, 3, strides=1, padding='same', activation='sigmoid')(x)

        return x

    def call(self, inputs, training=False):
        """前向传播 - 使用更合理的特征重建策略"""
        # inputs shape: [batch, compressed_features]
        batch_size = tf.shape(inputs)[0]

        # 第一步：逐步扩展特征维度，避免巨大的Dense层
        # 从1024维开始逐步扩展
        x = tf.keras.layers.Dense(2048, activation='relu')(inputs)
        x = tf.keras.layers.Dropout(0.1)(x, training=training)

        x = tf.keras.layers.Dense(4096, activation='relu')(x)
        x = tf.keras.layers.Dropout(0.1)(x, training=training)

        # 扩展到一个合理的中间维度
        x = tf.keras.layers.Dense(8192, activation='relu')(x)
        x = tf.keras.layers.Dropout(0.1)(x, training=training)

        # 重塑为小的特征图，然后用卷积扩展
        # 重塑为 8x16x64 的特征图
        x = tf.reshape(x, [batch_size, 8, 16, 64])

        # 第二步：使用转置卷积逐步上采样
        # 8x16 -> 16x32
        x = tf.keras.layers.Conv2DTranspose(128, 4, strides=2, padding='same', activation='relu')(x)
        x = tf.keras.layers.BatchNormalization()(x, training=training)

        # 16x32 -> 32x64 (目标压缩尺寸)
        x = tf.keras.layers.Conv2DTranspose(self.embedding_dim, 4, strides=2, padding='same', activation='relu')(x)
        x = tf.keras.layers.BatchNormalization()(x, training=training)
        # x shape: [batch, 32, 64, embedding_dim]

        # 第三步：CNN上采样重建到原始图像尺寸
        reconstructed_image = self._cnn_upsampling(x, training=training)
        # reconstructed_image shape: [batch, height, width, channels]

        return reconstructed_image
