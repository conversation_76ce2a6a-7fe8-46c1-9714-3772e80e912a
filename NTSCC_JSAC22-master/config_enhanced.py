import torch
import datetime
import torch.nn as nn


class EnhancedConfig:
    """
    增强版NTSCC配置，集成Image JSCC的信道处理功能
    包括OFDM调制、量化处理和比特传输等功能
    """

    # 基础训练配置
    train_data_dir = ['data/train2017']
    test_data_dir = ['data/kodak']
    batch_size = 10
    num_workers = 8

    print_step = 50
    plot_step = 1000
    logger = None

    # 训练参数
    image_dims = (3, 256, 256)
    lr = 1e-4
    aux_lr = 1e-3
    distortion_metric = 'MSE'  # 'MS-SSIM'

    use_side_info = False
    train_lambda = 64
    eta = 0.2

    # 增强的信道配置 - 集成Image JSCC的完整信道处理
    channel = {
        # 基础信道参数
        "type": 'awgn',
        'chan_param': 10,  # SNR in dB
        'modulation': 'qpsk',  # 'none', 'qpsk', 'bpsk', '16qam'
        'symbol_energy': 1.0,   # 符号能量
        
        # OFDM参数 (来自Image JSCC)
        'enable_ofdm': True,     # 是否启用OFDM
        'num_ofdm_symbols': 1,
        'fft_size': 64,
        'num_subcarriers': 64,
        'subcarrier_spacing': 15e3,  # Hz
        'carrier_freq': 3.5e9,       # Hz
        'delay_spread': 100e-9,      # 秒
        'cp_length': 16,
        'num_bits_per_symbol': 2,    # QPSK
        
        # 量化参数 (来自Image JSCC)
        'enable_quantization': True,  # 是否启用量化
        'quantization_bits': 6,       # 量化比特数
        'quantization_method': 'uniform',  # 量化方法
        'quantization_range': [-1.0, 1.0],  # 量化范围
        
        # 比特传输参数
        'enable_bit_transmission': True,  # 是否启用比特级传输
        'bit_interleaving': True,         # 是否启用比特交织
        'error_correction': False,        # 是否启用错误纠正
        
        # 信道编码参数
        'channel_coding': {
            'enable': False,
            'type': 'ldpc',  # 'ldpc', 'turbo', 'polar'
            'code_rate': 0.5,
            'block_length': 1024
        },
        
        # 多径信道参数
        'multipath': {
            'enable': False,
            'num_paths': 3,
            'path_delays': [0, 50e-9, 100e-9],  # 秒
            'path_gains': [1.0, 0.5, 0.3],      # 线性增益
        }
    }

    # 速率自适应参数
    multiple_rate = [16, 32, 48, 64, 80, 96, 102, 118, 134, 160, 186, 192, 208, 224, 240, 256]
    
    # 模型架构参数
    ga_kwargs = dict(
        img_size=(image_dims[1], image_dims[2]),
        embed_dims=[256, 256, 256, 256], 
        depths=[1, 1, 2, 4], 
        num_heads=[8, 8, 8, 8],
        window_size=8, 
        mlp_ratio=4., 
        qkv_bias=True, 
        qk_scale=None,
        norm_layer=nn.LayerNorm, 
        patch_norm=True,
    )

    gs_kwargs = dict(
        img_size=(image_dims[1], image_dims[2]),
        embed_dims=[256, 256, 256, 256], 
        depths=[4, 2, 1, 1], 
        num_heads=[8, 8, 8, 8],
        window_size=8, 
        mlp_ratio=4., 
        norm_layer=nn.LayerNorm, 
        patch_norm=True
    )

    fe_kwargs = dict(
        input_resolution=(image_dims[1] // 16, image_dims[2] // 16),
        embed_dim=256, 
        depths=[4], 
        num_heads=[8],
        window_size=16, 
        mlp_ratio=4., 
        qkv_bias=True, 
        qk_scale=None,
        norm_layer=nn.LayerNorm, 
        rate_choice=multiple_rate
    )

    fd_kwargs = dict(
        input_resolution=(image_dims[1] // 16, image_dims[2] // 16),
        embed_dim=256, 
        depths=[4], 
        num_heads=[8],
        window_size=16, 
        mlp_ratio=4., 
        qkv_bias=True, 
        qk_scale=None,
        norm_layer=nn.LayerNorm, 
        rate_choice=multiple_rate
    )

    # 设备配置
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 实验配置
    experiment_name = f"NTSCC_Enhanced_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"
    save_path = f"./checkpoints/{experiment_name}/"
    
    # 日志配置
    log_level = 'INFO'
    save_logs = True
    
    @classmethod
    def get_channel_info(cls):
        """获取信道配置信息"""
        info = {
            'type': cls.channel['type'],
            'snr_db': cls.channel['chan_param'],
            'modulation': cls.channel['modulation'],
            'ofdm_enabled': cls.channel.get('enable_ofdm', False),
            'quantization_enabled': cls.channel.get('enable_quantization', False),
            'quantization_bits': cls.channel.get('quantization_bits', 0),
        }
        return info
    
    @classmethod
    def print_config(cls):
        """打印配置信息"""
        print("=" * 60)
        print("Enhanced NTSCC Configuration")
        print("=" * 60)
        print(f"Image dimensions: {cls.image_dims}")
        print(f"Batch size: {cls.batch_size}")
        print(f"Learning rate: {cls.lr}")
        print(f"Channel type: {cls.channel['type']}")
        print(f"SNR: {cls.channel['chan_param']} dB")
        print(f"Modulation: {cls.channel['modulation']}")
        print(f"OFDM enabled: {cls.channel.get('enable_ofdm', False)}")
        if cls.channel.get('enable_ofdm', False):
            print(f"  FFT size: {cls.channel['fft_size']}")
            print(f"  Subcarriers: {cls.channel['num_subcarriers']}")
            print(f"  CP length: {cls.channel['cp_length']}")
        print(f"Quantization enabled: {cls.channel.get('enable_quantization', False)}")
        if cls.channel.get('enable_quantization', False):
            print(f"  Quantization bits: {cls.channel['quantization_bits']}")
            print(f"  Quantization method: {cls.channel['quantization_method']}")
        print(f"Device: {cls.device}")
        print("=" * 60)


# 兼容性别名
config = EnhancedConfig
