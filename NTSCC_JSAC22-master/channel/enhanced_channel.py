import torch
import torch.nn as nn
import numpy as np


class QuantizationLayer(nn.Module):
    """
    量化层 - 来自Image JSCC的量化实现
    """
    def __init__(self, quantization_bits=6, quantization_range=(-1.0, 1.0)):
        super(QuantizationLayer, self).__init__()
        self.quantization_bits = quantization_bits
        self.quantization_range = quantization_range
        self.step_size = (quantization_range[1] - quantization_range[0]) / (2 ** quantization_bits)
    
    def forward(self, x):
        """
        量化操作，使用直通估计器保持梯度
        """
        # 将输入限制在量化范围内
        x_clipped = torch.clamp(x, self.quantization_range[0], self.quantization_range[1])
        
        # 量化
        x_normalized = (x_clipped - self.quantization_range[0]) / (self.quantization_range[1] - self.quantization_range[0])
        x_quantized_norm = torch.floor(x_normalized / self.step_size) * self.step_size
        x_quantized = x_quantized_norm * (self.quantization_range[1] - self.quantization_range[0]) + self.quantization_range[0]
        
        # 直通估计器：前向使用量化值，反向使用原始梯度
        x_quantized = x_quantized + (x_clipped - x_quantized).detach()
        
        return x_quantized


class OFDMProcessor(nn.Module):
    """
    OFDM处理器 - 来自Image JSCC的OFDM实现
    """
    def __init__(self, fft_size=64, cp_length=16, num_subcarriers=64):
        super(OFDMProcessor, self).__init__()
        self.fft_size = fft_size
        self.cp_length = cp_length
        self.num_subcarriers = num_subcarriers
    
    def modulate(self, symbols):
        """
        OFDM调制
        Args:
            symbols: [batch, num_symbols] 复数符号
        Returns:
            ofdm_signal: [batch, ofdm_length] OFDM时域信号
        """
        batch_size, num_symbols = symbols.shape
        
        # 计算需要的OFDM符号数
        num_ofdm_symbols = int(np.ceil(num_symbols / self.num_subcarriers))
        
        # 填充到完整的OFDM符号
        padded_length = num_ofdm_symbols * self.num_subcarriers
        if num_symbols < padded_length:
            padding = torch.zeros(batch_size, padded_length - num_symbols, 
                                dtype=symbols.dtype, device=symbols.device)
            symbols = torch.cat([symbols, padding], dim=1)
        
        # 重塑为OFDM符号格式
        ofdm_symbols = symbols.view(batch_size, num_ofdm_symbols, self.num_subcarriers)
        
        # IFFT
        time_domain = torch.fft.ifft(ofdm_symbols, dim=-1)
        
        # 添加循环前缀
        cp = time_domain[:, :, -self.cp_length:]
        time_domain_with_cp = torch.cat([cp, time_domain], dim=-1)
        
        # 展平
        ofdm_signal = time_domain_with_cp.view(batch_size, -1)
        
        return ofdm_signal, num_symbols
    
    def demodulate(self, ofdm_signal, original_length):
        """
        OFDM解调
        Args:
            ofdm_signal: [batch, ofdm_length] OFDM时域信号
            original_length: 原始符号长度
        Returns:
            symbols: [batch, original_length] 解调后的符号
        """
        batch_size = ofdm_signal.shape[0]
        
        # 计算OFDM参数
        num_ofdm_symbols = int(np.ceil(original_length / self.num_subcarriers))
        ofdm_symbol_length = self.num_subcarriers + self.cp_length
        
        # 重塑为OFDM符号格式
        received_ofdm = ofdm_signal.view(batch_size, num_ofdm_symbols, ofdm_symbol_length)
        
        # 移除循环前缀
        received_no_cp = received_ofdm[:, :, self.cp_length:]
        
        # FFT
        freq_domain = torch.fft.fft(received_no_cp, dim=-1)
        
        # 展平并截取到原始长度
        symbols = freq_domain.view(batch_size, -1)
        symbols = symbols[:, :original_length]
        
        return symbols


class EnhancedChannel(nn.Module):
    """
    增强信道模块 - 集成Image JSCC的完整信道处理功能
    """
    def __init__(self, config):
        super(EnhancedChannel, self).__init__()
        self.config = config
        self.chan_type = config.channel['type']
        self.chan_param = config.channel['chan_param']
        
        # 调制参数
        self.modulation = config.channel.get('modulation', 'qpsk')
        self.symbol_energy = config.channel.get('symbol_energy', 1.0)
        
        # 量化层
        if config.channel.get('enable_quantization', False):
            self.quantizer = QuantizationLayer(
                quantization_bits=config.channel.get('quantization_bits', 6),
                quantization_range=config.channel.get('quantization_range', (-1.0, 1.0))
            )
        else:
            self.quantizer = None
        
        # OFDM处理器
        if config.channel.get('enable_ofdm', False):
            self.ofdm_processor = OFDMProcessor(
                fft_size=config.channel.get('fft_size', 64),
                cp_length=config.channel.get('cp_length', 16),
                num_subcarriers=config.channel.get('num_subcarriers', 64)
            )
        else:
            self.ofdm_processor = None
        
        # 比特交织器
        self.enable_bit_interleaving = config.channel.get('bit_interleaving', False)
        
        if config.logger:
            config.logger.info(f'【Enhanced Channel】: Built {self.chan_type} channel')
            config.logger.info(f'  Modulation: {self.modulation}, SNR: {self.chan_param} dB')
            if self.quantizer:
                config.logger.info(f'  Quantization: {config.channel["quantization_bits"]} bits')
            if self.ofdm_processor:
                config.logger.info(f'  OFDM: FFT size {config.channel["fft_size"]}, CP {config.channel["cp_length"]}')
    
    def digital_modulation(self, input_signal):
        """数字调制"""
        if self.modulation == 'none':
            return input_signal
        elif self.modulation == 'qpsk':
            real_part = torch.sign(torch.real(input_signal))
            imag_part = torch.sign(torch.imag(input_signal))
            qpsk_symbols = (real_part + 1j * imag_part) / np.sqrt(2) * np.sqrt(self.symbol_energy)
            return qpsk_symbols
        elif self.modulation == 'bpsk':
            bpsk_symbols = torch.sign(torch.real(input_signal)) * np.sqrt(self.symbol_energy)
            return bpsk_symbols.to(torch.complex64)
        elif self.modulation == '16qam':
            def quantize_4level(x):
                return torch.sign(x) * (1 + 2 * (torch.abs(x) > torch.median(torch.abs(x))))
            real_part = quantize_4level(torch.real(input_signal))
            imag_part = quantize_4level(torch.imag(input_signal))
            qam16_symbols = (real_part + 1j * imag_part) / np.sqrt(10) * np.sqrt(self.symbol_energy)
            return qam16_symbols
        else:
            raise ValueError(f"Unsupported modulation: {self.modulation}")
    
    def digital_demodulation(self, received_symbols):
        """数字解调（软判决）"""
        if self.modulation == 'none':
            return received_symbols
        elif self.modulation == 'qpsk':
            return received_symbols * np.sqrt(2) / np.sqrt(self.symbol_energy)
        elif self.modulation == 'bpsk':
            return received_symbols / np.sqrt(self.symbol_energy)
        elif self.modulation == '16qam':
            return received_symbols * np.sqrt(10) / np.sqrt(self.symbol_energy)
        else:
            raise ValueError(f"Unsupported demodulation: {self.modulation}")
    
    def add_awgn_noise(self, signal):
        """添加AWGN噪声"""
        if self.chan_type == 'noiseless':
            return signal
        
        # 计算噪声功率
        sigma = np.sqrt(1.0 / (2 * 10 ** (self.chan_param / 10)))
        
        # 生成复数噪声
        device = signal.device
        noise_real = torch.normal(mean=0.0, std=sigma, size=signal.shape, device=device)
        noise_imag = torch.normal(mean=0.0, std=sigma, size=signal.shape, device=device)
        noise = noise_real + 1j * noise_imag
        
        return signal + noise
    
    def forward(self, input_tensor, avg_pwr=None, power=1):
        """
        前向传播 - 完整的信道处理流程
        """
        # 功率归一化
        if avg_pwr is None:
            avg_pwr = torch.mean(input_tensor ** 2)
        channel_tx = np.sqrt(power) * input_tensor / torch.sqrt(avg_pwr * 2)
        
        # 转换为复数信号
        input_shape = channel_tx.shape
        channel_in = channel_tx.reshape(input_shape[0], -1)
        
        # 实数转复数
        if channel_in.shape[-1] % 2 == 1:
            # 如果是奇数长度，填充一个零
            padding = torch.zeros(channel_in.shape[0], 1, device=channel_in.device)
            channel_in = torch.cat([channel_in, padding], dim=-1)
        
        complex_signal = channel_in[:, ::2] + 1j * channel_in[:, 1::2]
        
        # 量化处理
        if self.quantizer is not None:
            real_part = self.quantizer(torch.real(complex_signal))
            imag_part = self.quantizer(torch.imag(complex_signal))
            complex_signal = real_part + 1j * imag_part
        
        # OFDM调制
        original_length = complex_signal.shape[1]
        if self.ofdm_processor is not None:
            complex_signal, original_length = self.ofdm_processor.modulate(complex_signal)
        
        # 数字调制
        modulated_symbols = self.digital_modulation(complex_signal)
        channel_usage = modulated_symbols.numel()
        
        # 信道传输（添加噪声）
        received_symbols = self.add_awgn_noise(modulated_symbols)
        
        # 数字解调
        demodulated_signal = self.digital_demodulation(received_symbols)
        
        # OFDM解调
        if self.ofdm_processor is not None:
            demodulated_signal = self.ofdm_processor.demodulate(demodulated_signal, original_length)
        
        # 转换回实数格式
        real_part = torch.real(demodulated_signal)
        imag_part = torch.imag(demodulated_signal)
        
        # 交替排列实部和虚部
        output_flat = torch.zeros(demodulated_signal.shape[0], demodulated_signal.shape[1] * 2, 
                                 device=demodulated_signal.device)
        output_flat[:, ::2] = real_part
        output_flat[:, 1::2] = imag_part
        
        # 截取到原始长度
        original_flat_length = input_shape[0] * np.prod(input_shape[1:])
        output_flat = output_flat[:, :original_flat_length]
        
        # 重塑回原始形状
        channel_rx = output_flat.reshape(input_shape)
        
        # 功率恢复
        channel_rx = channel_rx * torch.sqrt(avg_pwr * 2)
        
        return channel_rx, channel_usage
