import torch.nn as nn
import numpy as np
import os
import torch


class Channel(nn.Module):
    def __init__(self, config):
        super(Channel, self).__init__()
        self.config = config
        self.chan_type = config.channel['type']
        self.chan_param = config.channel['chan_param']
        self.device = config.device

        # 数字调制配置
        self.modulation = config.channel.get('modulation', 'none')  # 'none', 'qpsk', 'bpsk', '16qam'
        self.symbol_energy = config.channel.get('symbol_energy', 1.0)

        # OFDM参数 (来自Image JSCC)
        self.num_ofdm_symbols = config.channel.get('num_ofdm_symbols', 1)
        self.fft_size = config.channel.get('fft_size', 64)
        self.num_subcarriers = config.channel.get('num_subcarriers', 64)
        self.subcarrier_spacing = config.channel.get('subcarrier_spacing', 15e3)
        self.carrier_freq = config.channel.get('carrier_freq', 3.5e9)
        self.delay_spread = config.channel.get('delay_spread', 100e-9)
        self.cp_length = config.channel.get('cp_length', 16)
        self.num_bits_per_symbol = config.channel.get('num_bits_per_symbol', 2)

        # 量化参数 (来自Image JSCC)
        self.quantization_bits = config.channel.get('quantization_bits', 6)
        self.enable_quantization = config.channel.get('enable_quantization', True)
        self.quantization_method = config.channel.get('quantization_method', 'uniform')

        if config.logger:
            if self.modulation != 'none':
                config.logger.info('【Channel】: Built {} channel with {} modulation, SNR {} dB.'.format(
                    config.channel['type'], self.modulation.upper(), config.channel['chan_param']))
                config.logger.info('【Channel】: OFDM enabled - FFT size: {}, Subcarriers: {}, CP length: {}'.format(
                    self.fft_size, self.num_subcarriers, self.cp_length))
                if self.enable_quantization:
                    config.logger.info('【Channel】: Quantization enabled - {} bits, method: {}'.format(
                        self.quantization_bits, self.quantization_method))
            else:
                config.logger.info('【Channel】: Built {} channel, SNR {} dB.'.format(
                    config.channel['type'], config.channel['chan_param']))

    def gaussian_noise_layer(self, input_layer, std):
        device = input_layer.device if input_layer.device.index is not None else torch.device('cpu')
        noise_real = torch.normal(mean=0.0, std=std, size=np.shape(input_layer), device=device)
        noise_imag = torch.normal(mean=0.0, std=std, size=np.shape(input_layer), device=device)
        noise = noise_real + 1j * noise_imag
        return input_layer + noise

    def quantize_signal(self, input_signal):
        """
        量化信号 (来自Image JSCC的量化方法)
        Args:
            input_signal: 输入信号 tensor
        Returns:
            quantized_signal: 量化后的信号
        """
        if not self.enable_quantization:
            return input_signal

        if self.quantization_method == 'uniform':
            # 均匀量化
            step_size = 2.0 / (2 ** self.quantization_bits)
            # 将信号范围映射到 [0, 1]
            normalized = (input_signal + 1.0) / 2.0
            # 量化
            quantized_normalized = torch.floor(normalized / step_size) * step_size
            # 添加量化噪声 (直通估计器)
            quantized_normalized = quantized_normalized + (normalized - quantized_normalized).detach()
            # 映射回原始范围
            quantized = quantized_normalized * 2.0 - 1.0
            return quantized
        else:
            return input_signal

    def ofdm_modulation(self, input_signal):
        """
        OFDM调制 (来自Image JSCC的OFDM实现)
        Args:
            input_signal: 复数信号 tensor
        Returns:
            ofdm_signal: OFDM调制后的信号
        """
        # 重塑信号以适应OFDM子载波
        batch_size = input_signal.shape[0]
        signal_length = input_signal.shape[1]

        # 计算需要的OFDM符号数
        symbols_per_ofdm = self.num_subcarriers
        num_ofdm_symbols = int(np.ceil(signal_length / symbols_per_ofdm))

        # 填充到完整的OFDM符号
        padded_length = num_ofdm_symbols * symbols_per_ofdm
        if signal_length < padded_length:
            padding = torch.zeros(batch_size, padded_length - signal_length,
                                dtype=input_signal.dtype, device=input_signal.device)
            input_signal = torch.cat([input_signal, padding], dim=1)

        # 重塑为OFDM符号格式
        ofdm_symbols = input_signal.view(batch_size, num_ofdm_symbols, self.num_subcarriers)

        # IFFT (OFDM调制)
        time_domain = torch.fft.ifft(ofdm_symbols, dim=-1)

        # 添加循环前缀
        cp = time_domain[:, :, -self.cp_length:]
        time_domain_with_cp = torch.cat([cp, time_domain], dim=-1)

        # 展平为一维信号
        ofdm_signal = time_domain_with_cp.view(batch_size, -1)

        return ofdm_signal, signal_length  # 返回原始长度用于解调

    def ofdm_demodulation(self, received_signal, original_length):
        """
        OFDM解调 (来自Image JSCC的OFDM实现)
        Args:
            received_signal: 接收到的OFDM信号
            original_length: 原始信号长度
        Returns:
            demodulated_signal: 解调后的信号
        """
        batch_size = received_signal.shape[0]

        # 计算OFDM参数
        symbols_per_ofdm = self.num_subcarriers
        num_ofdm_symbols = int(np.ceil(original_length / symbols_per_ofdm))
        ofdm_symbol_length = self.num_subcarriers + self.cp_length

        # 重塑为OFDM符号格式
        received_ofdm = received_signal.view(batch_size, num_ofdm_symbols, ofdm_symbol_length)

        # 移除循环前缀
        received_no_cp = received_ofdm[:, :, self.cp_length:]

        # FFT (OFDM解调)
        freq_domain = torch.fft.fft(received_no_cp, dim=-1)

        # 展平并截取到原始长度
        demodulated = freq_domain.view(batch_size, -1)
        demodulated = demodulated[:, :original_length]

        return demodulated

    def digital_modulation(self, input_signal):
        """
        数字调制：将连续信号映射到离散调制符号
        Args:
            input_signal: 复数信号 tensor
        Returns:
            modulated_symbols: 调制后的符号
        """
        if self.modulation == 'none':
            return input_signal

        elif self.modulation == 'qpsk':
            # QPSK调制：将复数信号量化到4个星座点
            real_part = torch.sign(torch.real(input_signal))
            imag_part = torch.sign(torch.imag(input_signal))
            # QPSK星座点：{±1±1j}/√2，归一化能量
            qpsk_symbols = (real_part + 1j * imag_part) / np.sqrt(2) * np.sqrt(self.symbol_energy)
            return qpsk_symbols

        elif self.modulation == 'bpsk':
            # BPSK调制：仅使用实部
            bpsk_symbols = torch.sign(torch.real(input_signal)) * np.sqrt(self.symbol_energy)
            return bpsk_symbols.to(torch.complex64)

        elif self.modulation == '16qam':
            # 16QAM调制：4级幅度量化
            def quantize_4level(x):
                return torch.sign(x) * (1 + 2 * (torch.abs(x) > torch.median(torch.abs(x))))

            real_part = quantize_4level(torch.real(input_signal))
            imag_part = quantize_4level(torch.imag(input_signal))
            # 16QAM归一化
            qam16_symbols = (real_part + 1j * imag_part) / np.sqrt(10) * np.sqrt(self.symbol_energy)
            return qam16_symbols

        else:
            raise ValueError(f"Unsupported modulation: {self.modulation}")

    def digital_demodulation(self, received_symbols):
        """
        数字解调：软判决解调（保持梯度）
        Args:
            received_symbols: 接收到的含噪符号
        Returns:
            demodulated_signal: 解调后的软信号
        """
        if self.modulation == 'none':
            return received_symbols

        elif self.modulation == 'qpsk':
            # QPSK软解调：直接返回接收符号（软判决）
            return received_symbols * np.sqrt(2) / np.sqrt(self.symbol_energy)

        elif self.modulation == 'bpsk':
            # BPSK软解调
            return received_symbols / np.sqrt(self.symbol_energy)

        elif self.modulation == '16qam':
            # 16QAM软解调
            return received_symbols * np.sqrt(10) / np.sqrt(self.symbol_energy)

        else:
            raise ValueError(f"Unsupported demodulation: {self.modulation}")

    def forward(self, input, avg_pwr=None, power=1):
        if avg_pwr is None:
            avg_pwr = torch.mean(input ** 2)
            channel_tx = np.sqrt(power) * input / torch.sqrt(avg_pwr * 2)
        else:
            channel_tx = np.sqrt(power) * input / torch.sqrt(avg_pwr * 2)
        input_shape = channel_tx.shape
        channel_in = channel_tx.reshape(-1)
        channel_in = channel_in[::2] + channel_in[1::2] * 1j

        # 量化处理 (来自Image JSCC)
        if self.enable_quantization:
            # 对实部和虚部分别量化
            real_part = torch.real(channel_in)
            imag_part = torch.imag(channel_in)
            real_quantized = self.quantize_signal(real_part)
            imag_quantized = self.quantize_signal(imag_part)
            channel_in = real_quantized + 1j * imag_quantized

        # OFDM调制 (如果启用)
        original_length = channel_in.shape[0]
        if hasattr(self, 'fft_size') and self.fft_size > 1:
            # 重塑为批次格式进行OFDM处理
            batch_size = input_shape[0]
            channel_in_batch = channel_in.view(batch_size, -1)
            ofdm_signal, original_signal_length = self.ofdm_modulation(channel_in_batch)
            channel_in = ofdm_signal.view(-1)

        # 数字调制
        modulated_symbols = self.digital_modulation(channel_in)
        channel_usage = modulated_symbols.numel()

        # 信道传输（在调制符号上加噪声）
        channel_output = self.channel_forward(modulated_symbols)

        # 数字解调
        demodulated_signal = self.digital_demodulation(channel_output)

        # OFDM解调 (如果启用)
        if hasattr(self, 'fft_size') and self.fft_size > 1:
            batch_size = input_shape[0]
            demodulated_batch = demodulated_signal.view(batch_size, -1)
            demodulated_signal = self.ofdm_demodulation(demodulated_batch, original_signal_length)
            demodulated_signal = demodulated_signal.view(-1)

        # 转换回实数格式
        channel_rx = torch.zeros_like(channel_tx.reshape(-1))
        channel_rx[::2] = torch.real(demodulated_signal)
        channel_rx[1::2] = torch.imag(demodulated_signal)
        channel_rx = channel_rx.reshape(input_shape)
        return channel_rx * torch.sqrt(avg_pwr * 2), channel_usage

    def channel_forward(self, channel_in):
        if self.chan_type == 0 or self.chan_type == 'noiseless':
            return channel_in

        elif self.chan_type == 1 or self.chan_type == 'awgn':
            channel_tx = channel_in
            sigma = np.sqrt(1.0 / (2 * 10 ** (self.chan_param / 10)))
            chan_output = self.gaussian_noise_layer(channel_tx,
                                                    std=sigma)
            return chan_output